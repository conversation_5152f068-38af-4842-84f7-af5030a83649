package com.sam.util;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-16 18:44
 * 上传至阿里云OSS工具类
 */
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyuncs.exceptions.ClientException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

@Component
public class AliyunOssUtil {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    @Value("${aliyun.oss.dir.prefix}")
    private String dirPrefix;

    /**
     * 文件上传方法
     * @param file 要上传的文件对象 (MultipartFile)
     * @return 上传成功后的文件URL
     */
    public String upload(MultipartFile file) {
        OSS ossClient = null;
        try {
            // 从环境变量中获取访问凭证，这是你希望的方式
            CredentialsProvider credentialsProvider = CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();

            // 创建OSSClient实例。
            ossClient = new OSSClientBuilder().build(endpoint, credentialsProvider);

            // 生成文件名，确保唯一性
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String newFileName = UUID.randomUUID().toString() + extension;
            String filePath = dirPrefix + newFileName;

            // 上传文件到OSS
            try (InputStream inputStream = file.getInputStream()) {
                ossClient.putObject(bucketName, filePath, inputStream);
            }

            // 拼接文件URL
            return "https://" + bucketName + "." + endpoint.substring(endpoint.indexOf("://") + 3) + "/" + filePath;
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("文件上传失败", e);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        } finally {
            if (ossClient != null) {
                // 确保OSSClient实例被关闭以释放资源
                ossClient.shutdown();
            }
        }
    }
}
# Spring Boot 基础配置
spring:
  # 数据库连接配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver # 根据你使用的数据库类型修改，例如MySQL
    url: ************************************************************************************************************************
    username: root
    password: '00000000'

  # 阿里云 OSS 配置
  aliyun:
    oss:
      endpoint: https://oss-cn-wuhan-lr.aliyuncs.com
      bucketName: sunpayus # 你的Bucket名称
      dir:
        prefix: images/ # 上传文件的前缀目录，可选

# Druid 连接池监控配置
druid:
  # 连接池参数配置
  filter:
    stat:
      enabled: true
      log-slow-sql: true
      slow-sql-millis: 2000 # 慢SQL阈值，单位毫秒

# MyBatis-Plus 配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml # Mapper XML 文件位置
  type-aliases-package: com.sam.entity # 你的实体类包名
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 打印SQL到控制台
    map-underscore-to-camel-case: true # 开启驼峰命名转换